INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('admin', '1234', '<EMAIL>', true);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user', '1234', '<EMAIL>', false);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user2', '1234', '<EMAIL>', false);

INSERT INTO "User" (username, password, email, isAdmin)
VALUES ('user3', '1234', '<EMAIL>', false);


-- Insert data into Media table
INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (200000, 'Sample Book', 100, 1, 'This is a sample book', '2022-01-01', true, 'sample_book.jpg', '12x6x9', '12345');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (150000, 'Sample CD', 50, 0.25, 'This is a sample CD', '2022-01-01', false, 'sample_cd.jpg', '15x6x9', '56789');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Sample DVD', 75, 0.25, 'This is a sample DVD', '2022-01-01', true, 'sample_dvd.jpg', '17x6x9', '98465');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (1360000, 'Coffe Mate', 60, 1.1, 'A robotic romance', '2025-06-25', false, 'CoffMate.png', '20x24x2', '82234');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (200000, 'Dont Touch', 65, 0.6, 'A fantasy', '2025-06-26', false, 'donTouch.png', '20x24x2', '45234');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (150000, 'Enter the Ether', 85, 0.4, 'Suspense ?', '2025-06-26', false, 'enterEther.png', '20x24x2', '54783');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (350000, 'First Piece of Soil', 90, 1.0, 'If you are into history', '2025-06-26', true, 'firstSoil.png', '20x24x2', '14256');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (150000, 'Invasion: Next Gen', 70, 0.8, 'Lots of action', '2025-06-26', true, 'invaNG.png', '20x24x2', '29545');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Nihility', 80, 0.6, 'Void', '2025-06-26', true, 'nihility.png', '20x24x2', '53494');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (350000, 'Starlight Knight', 91, 0.6, 'A hero team', '2025-06-26', false, 'starlight.png', '20x24x2', '65342');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'The Big Hollow', 80, 0.6, 'Best of 2025', '2025-06-26', true, 'bigHollow.png', '20x24x2', '59832');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (100000, 'The Heartbeat', 80, 0.6, 'Best of 2025', '2025-06-26', false, 'heartbeat.png', '20x24x2', '23918');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'The Ridu Tour', 78, 0.6, 'For tourist', '2025-06-26', true, 'riduTour.png', '20x24x2', '78183');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (270000, '7710 and Its Cat', 56, 0.6, 'Wholesome', '2025-06-26', true, 'cats.png', '20x24x2', '45903');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (210000, 'Attack on Cyberz', 60, 0.6, 'Best of 2025', '2025-06-26', true, 'Cyberz.png', '20x24x2', '42189');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Bangboo Knows', 65, 0.6, '3hrs of Bangboo', '2025-06-26', false, 'bangbooK.png', '20x24x2', '90109');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (120000, 'Beheboth', 80, 0.6, 'Best monster of 2025', '2025-06-26', true, 'beheboth.png', '20x24x2', '59470');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Dimensional Musketeer', 100, 0.6, 'Trendiest of 2025', '2025-06-26', true, 'dimensionMusket.png', '20x24x2', '46652');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (260000, 'Disappearing Elevator', 60, 0.6, 'Best of 2025', '2025-06-26', true, 'disapEle.png', '20x24x2', '45559');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Encore', 70, 0.6, 'Best of 2025', '2025-06-26', true, 'encore.png', '20x24x2', '58881');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (200000, 'Family', 62, 0.6, 'Best of 2025', '2025-06-26', true, 'family.png', '20x24x2', '57712');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Golorious Guardians', 40, 0.6, 'Interview', '2025-06-26', true, 'gloriousGuardians.png', '20x24x2', '33281');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (150000, 'Oh~ Sweetie', 60, 0.6, 'Best of 2025', '2025-06-26', true, 'sweetie.png', '20x24x2', '11123');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (300000, 'Small Body Big Crisis', 60, 0.4, 'Best of 2025', '2025-06-26', true, 'smallBodyBigCrisis.png', '20x24x2', '21532');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (350000, 'We Need You', 60, 0.6, 'Kind of american-ish', '2025-06-26', true, 'weNeedU.png', '20x24x2', '15832');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Back 2 Life', 60, 0.6, 'Best of 2025', '2025-06-26', true, 'B2L.png', '20x24x2', '58812');

INSERT INTO Media (price, title, totalQuantity, weight, description, importDate, rushOrderSupported, imageUrl, productDimension, barcode)
VALUES (250000, 'Best Bid', 60, 0.6, 'Gambling bros', '2025-06-26', true, 'bestBid.png', '20x24x2', '90012');
-- Insert data into Book table
INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (1, 'Author Name', 'Hardcover', 'Publisher Name', '2022-01-01', 300, 'English', 'Fiction');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (4, 'Lorenzo Wise', 'Hardcover', 'BIO', '2022-01-01', 300, 'English', 'Romantic');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (5, 'Alfred Martini', 'Hardcover', 'Amazon', '2022-01-01', 350, 'English', 'Novel');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (6, 'Kate Gasbin', 'Hardcover', 'PIMP', '2022-01-01', 440, 'English', 'Comedy');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (7, 'Kiley Roy', 'Hardcover', 'Amazon', '2022-01-01', 500, 'English', 'Comedy');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (8, 'Alexandre Tannax', 'Hardcover', 'BIO', '2022-01-01', 300, 'English', 'Novel');

INSERT INTO Book (media_id, authors, coverType, publisher, publicationDate, pages, language, genre)
VALUES (9, 'Alejandro Martini', 'Hardcover', 'PIMP', '2022-01-01', 500, 'English', 'Fiction');

-- Insert data into CD_and_LP table
INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (2, true, 'None of Them', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-01-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (10, true, 'Sam', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-03-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (11, true, 'Nox Vertex', 'Record Label', 'Track 1, Track 2, Track 3', 'Pop', '2022-02-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (12, true, 'Last Chance', 'Universal Record', 'Track 1, Track 2, Track 3', 'Pop', '2022-06-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (13, false, 'Maximum', 'Universal Record', 'Track 1, Track 2, Track 3', 'Pop & Rap', '2022-05-01');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (14, true, 'M3P0', 'YG', 'Track 1, Track 2, Track 3', 'KPop', '2023-10-04');

INSERT INTO CD_and_LP (media_id, isCD, artists, recordLabel, trackList, genre, releaseDate)
VALUES (15, false, 'Vivian', 'YG', 'Track 1, Track 2, Track 3', 'KPop', '2022-01-01');

-- Insert data into DVD table
INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (3, 'Movie', 'Loxurious', 120, 'Light Face', 'English', 'English', '1993-06-11', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (16, 'Movie', 'Steven Universe', 145, 'Universe Pictures', 'English', 'English', '2003-06-10', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (17, 'Movie', 'Leo', 140, 'Universal Pictures', 'English', 'English', '2018-09-12', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (18, 'Movie', 'Vasx', 130, 'MBX', 'English', 'English', '2020-10-12', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (19, 'Movie', 'Bay Mike', 135, 'RiduExp', 'English', 'English', '2018-10-01', 'Adventure');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (20, 'Movie', 'Vers', 170, 'Best Yappers', 'English', 'Vietnamese', '2023-06-11', 'Romantic');

INSERT INTO DVD (media_id, dvdType, director, runtime, studio, language, subtitles, releasedDate, genre)
VALUES (21, 'Movie', 'Sherry Hormann', 160, 'Best Rizzlers', 'English', 'Korean', '2022-06-11', 'Novel');


-- Insert data into DeliveryInfo table
INSERT INTO DeliveryInfo (name, phone, email, province, address, message) 
VALUES ('Jane Doe', '0123456789', '<EMAIL>', 'Hanoi', '123 ABC Street', 'Do not drop it');

-- Insert data into OrderInfo table
INSERT INTO OrderInfo (shippingFees, subtotal, status, delivery_id) VALUES (100, 500, 'PENDING', 1);
INSERT INTO OrderInfo (shippingFees, subtotal, status, delivery_id) VALUES (666, 666, 'ACCEPTED', 1);

-- Insert data into RushOrderInfo table
INSERT INTO RushOrderInfo (deliveryTime, instruction, order_id) VALUES ('2022-12-31 23:59:59', 'Handle with care', 1);


-- Insert data into Order_Media table
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 1, 3, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 2, 2, 1);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (1, 3, 7, 0);

INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 1, 9, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 2, 9, 0);
INSERT INTO Order_Media (order_id, media_id, quantity, orderType) VALUES (2, 3, 9, 0);