<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.Cursor?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ChoiceBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.Separator?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.effect.DropShadow?>
<?import javafx.scene.image.Image?>
<?import javafx.scene.image.ImageView?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.BorderPane?>
<?import javafx.scene.layout.ColumnConstraints?>
<?import javafx.scene.layout.GridPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.RowConstraints?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);" xmlns="http://javafx.com/javafx/21" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.hust.ict.aims.view.place.ShippingScreenHandler">
    <BorderPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0">
        <top>
            <VBox style="-fx-background-color: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);" BorderPane.alignment="CENTER">
                <children>
                    <HBox alignment="CENTER" prefHeight="100.0" style="-fx-padding: 15;">
                        <children>
                            <ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true">
                                <image>
                                    <Image url="@../assets/images/Logo.png" />
                                </image>
                                <cursor>
                                    <Cursor fx:constant="HAND" />
                                </cursor>
                                <HBox.margin>
                                    <Insets right="20.0" />
                                </HBox.margin>
                            </ImageView>
                            <Separator maxHeight="-Infinity" maxWidth="-Infinity" orientation="VERTICAL" prefHeight="60.0" prefWidth="2.0" style="-fx-background-color: #dee2e6;" />
                            <VBox alignment="CENTER_LEFT" spacing="5.0">
                                <children>
                                    <Label text="DELIVERY INFORMATION" textFill="#dc3545">
                                        <font>
                                            <Font name="System Bold" size="28.0" />
                                        </font>
                                    </Label>
                                    <Label text="Complete your shipping details" textFill="#6c757d">
                                        <font>
                                            <Font size="16.0" />
                                        </font>
                                    </Label>
                                </children>
                                <HBox.margin>
                                    <Insets left="20.0" />
                                </HBox.margin>
                            </VBox>
                            <Region HBox.hgrow="ALWAYS" />
                        </children>
                    </HBox>
                </children>
            </VBox>
        </top>
        <center>
            <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;" BorderPane.alignment="CENTER">
                <content>
                    <VBox prefWidth="1326.0" spacing="30.0" style="-fx-padding: 30;">
                        <children>
                            <!-- Delivery Information Form Card -->
                            <VBox spacing="25.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 30; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                                <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                        <children>
                                            <Label text="📋" textFill="#dc3545">
                                                <font>
                                                    <Font size="24.0" />
                                                </font>
                                            </Label>
                                            <Label text="Shipping Information" textFill="#2c3e50">
                                                <font>
                                                    <Font name="System Bold" size="22.0" />
                                                </font>
                                            </Label>
                                        </children>
                                    </HBox>
                                    <Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

                                    <GridPane hgap="20.0" vgap="20.0">
                                        <columnConstraints>
                                            <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="150.0" />
                                            <ColumnConstraints hgrow="SOMETIMES" minWidth="400.0" prefWidth="600.0" />
                                        </columnConstraints>
                                        <rowConstraints>
                                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                            <RowConstraints minHeight="40.0" prefHeight="40.0" vgrow="SOMETIMES" />
                                            <RowConstraints minHeight="100.0" prefHeight="100.0" vgrow="SOMETIMES" />
                                        </rowConstraints>
                                        <children>
                                            <!-- Name Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="0">
                                                <children>
                                                    <Label text="Full Name" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="*" textFill="#dc3545">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <TextField fx:id="nameField" promptText="Enter your full name" style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                                            <!-- Phone Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="1">
                                                <children>
                                                    <Label text="Phone Number" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="*" textFill="#dc3545">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <TextField fx:id="phoneField" promptText="Enter your phone number" style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="1" />

                                            <!-- Email Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="2">
                                                <children>
                                                    <Label text="Email Address" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="*" textFill="#dc3545">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <TextField fx:id="emailField" promptText="Enter your email address" style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="2" />

                                            <!-- Province Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="3">
                                                <children>
                                                    <Label text="Province/City" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="*" textFill="#dc3545">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <ChoiceBox fx:id="provinceField" prefHeight="45.0" style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="3">
                                                <cursor>
                                                    <Cursor fx:constant="HAND" />
                                                </cursor>
                                            </ChoiceBox>

                                            <!-- Address Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="4">
                                                <children>
                                                    <Label text="Street Address" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="*" textFill="#dc3545">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <TextField fx:id="addressField" promptText="Enter your street address" style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="4" />

                                            <!-- Instructions Field -->
                                            <VBox spacing="5.0" GridPane.rowIndex="5">
                                                <children>
                                                    <Label text="Delivery Instructions" textFill="#6c757d">
                                                        <font>
                                                            <Font name="System Bold" size="14.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="(Optional)" textFill="#adb5bd">
                                                        <font>
                                                            <Font size="12.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                            <TextField fx:id="instructionsField" alignment="TOP_LEFT" prefHeight="80.0" promptText="Any special delivery instructions..." style="-fx-background-radius: 8; -fx-border-color: #dee2e6; -fx-border-radius: 8; -fx-padding: 12; -fx-font-size: 14;" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                                        </children>
                                    </GridPane>
                                </children>
                            </VBox>

                            <!-- Order Placement Section -->
                            <VBox spacing="20.0" style="-fx-background-color: white; -fx-background-radius: 15; -fx-padding: 25; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 10, 0, 0, 2);">
                                <children>
                                    <HBox alignment="CENTER_LEFT" spacing="10.0">
                                        <children>
                                            <Label text="📦" textFill="#28a745">
                                                <font>
                                                    <Font size="24.0" />
                                                </font>
                                            </Label>
                                            <Label text="Order Placement" textFill="#2c3e50">
                                                <font>
                                                    <Font name="System Bold" size="22.0" />
                                                </font>
                                            </Label>
                                        </children>
                                    </HBox>
                                    <Separator prefWidth="200.0" style="-fx-background-color: #e9ecef;" />

                                    <!-- Delivery Information Notes -->
                                    <VBox spacing="15.0" style="-fx-background-color: #e7f3ff; -fx-background-radius: 10; -fx-padding: 20;">
                                        <children>
                                            <HBox alignment="CENTER_LEFT" spacing="8.0">
                                                <children>
                                                    <Label text="💡" textFill="#0066cc">
                                                        <font>
                                                            <Font size="16.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="Important Delivery Notes" textFill="#0066cc">
                                                        <font>
                                                            <Font name="System Bold" size="16.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </HBox>
                                            <VBox spacing="8.0">
                                                <children>
                                                    <Label text="• Standard Delivery: 3-5 business days, free shipping for orders over $50" textFill="#2c3e50">
                                                        <font>
                                                            <Font size="13.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="• Rush Delivery: 1-2 business days, additional fees apply (available for selected areas)" textFill="#2c3e50">
                                                        <font>
                                                            <Font size="13.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="• Please ensure your contact information is accurate for delivery coordination" textFill="#2c3e50">
                                                        <font>
                                                            <Font size="13.0" />
                                                        </font>
                                                    </Label>
                                                    <Label text="• Orders will be saved to your account for tracking and history" textFill="#2c3e50">
                                                        <font>
                                                            <Font size="13.0" />
                                                        </font>
                                                    </Label>
                                                </children>
                                            </VBox>
                                        </children>
                                    </VBox>

                                    <!-- Required Fields Notice -->
                                    <HBox alignment="CENTER_LEFT" spacing="8.0" style="-fx-background-color: #fff3cd; -fx-background-radius: 8; -fx-padding: 12;">
                                        <children>
                                            <Label text="⚠️" textFill="#856404">
                                                <font>
                                                    <Font size="16.0" />
                                                </font>
                                            </Label>
                                            <Label text="Please fill in all required fields (*) before placing your order" textFill="#856404">
                                                <font>
                                                    <Font name="System Bold" size="14.0" />
                                                </font>
                                            </Label>
                                        </children>
                                    </HBox>

                                    <!-- Action Buttons -->
                                    <HBox alignment="CENTER" spacing="20.0" style="-fx-padding: 10 0;">
                                        <children>
                                            <Button fx:id="submitDeliveryInfoButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="200.0" style="-fx-background-color: #dc3545; -fx-background-radius: 25; -fx-text-fill: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);" text="📦 SUBMIT ORDER">
                                                <font>
                                                    <Font name="System Bold" size="14.0" />
                                                </font>
                                                <cursor>
                                                    <Cursor fx:constant="HAND" />
                                                </cursor>
                                            </Button>
                                            <Button fx:id="placeRushOrderButton" mnemonicParsing="false" prefHeight="50.0" prefWidth="200.0" style="-fx-background-color: #28a745; -fx-background-radius: 25; -fx-text-fill: white; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 8, 0, 0, 2);" text="⚡ RUSH ORDER">
                                                <font>
                                                    <Font name="System Bold" size="14.0" />
                                                </font>
                                                <cursor>
                                                    <Cursor fx:constant="HAND" />
                                                </cursor>
                                            </Button>
                                        </children>
                                    </HBox>
                                </children>
                            </VBox>
                        </children>
                    </VBox>
                </content>
            </ScrollPane>
        </center>
    </BorderPane>
</AnchorPane>
