<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.paint.*?>
<?import javafx.scene.text.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import java.lang.*?>
<?import javafx.scene.layout.*?>

<AnchorPane maxHeight="-Infinity" maxWidth="-Infinity" minHeight="-Infinity" minWidth="-Infinity" prefHeight="788.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;" xmlns="http://javafx.com/javafx/8" xmlns:fx="http://javafx.com/fxml/1">
   <children>
      <HBox prefHeight="104.0" prefWidth="1326.0" style="-fx-border-color: #33adff; -fx-background-color: white; -fx-padding: 10;" spacing="10.0" alignment="CENTER_LEFT">
         <children>
            <!-- Logo Section -->
            <HBox alignment="CENTER" prefWidth="150.0">
               <children>
                  <ImageView fx:id="aimsImage" fitHeight="80.0" fitWidth="80.0" pickOnBounds="true" preserveRatio="true" style="-fx-cursor: hand;">
                     <image>
                        <Image url="@logos/aim_logo.png" />
                     </image>
                  </ImageView>
               </children>
            </HBox>

            <!-- Search Section -->
            <HBox alignment="CENTER" spacing="10.0" HBox.hgrow="ALWAYS">
               <children>
                  <TextField fx:id="searchTextField" prefHeight="40.0" prefWidth="350.0" promptText="Search for products..." style="-fx-background-radius: 20; -fx-border-radius: 20; -fx-padding: 10;" />
                  <SplitMenuButton fx:id="splitMenuBtnSearch" mnemonicParsing="false" prefHeight="40.0" prefWidth="80.0" text="Search" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-background-radius: 20;">
                    <items>
                      <MenuItem mnemonicParsing="false" text="All" />
                      <MenuItem mnemonicParsing="false" text="Books" />
                      <MenuItem mnemonicParsing="false" text="DVDs" />
                      <MenuItem mnemonicParsing="false" text="CDs" />
                    </items>
                  </SplitMenuButton>
                  <ChoiceBox fx:id="choiceBoxOrder" prefHeight="40.0" prefWidth="100.0" style="-fx-background-radius: 20;" />
               </children>
            </HBox>

            <!-- Action Buttons Section -->
            <HBox alignment="CENTER_RIGHT" spacing="15.0" prefWidth="400.0">
               <children>
                  <Button fx:id="orderButton" mnemonicParsing="false" prefHeight="40.0" prefWidth="80.0" text="ORDER" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-background-radius: 20; -fx-font-weight: bold; -fx-font-size: 12px;" />
                  <Button fx:id="cartButton" mnemonicParsing="false" prefHeight="40.0" prefWidth="80.0" text="CART" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-background-radius: 20; -fx-font-weight: bold; -fx-font-size: 12px;" />
                  <Button fx:id="accountButton" mnemonicParsing="false" prefHeight="40.0" prefWidth="80.0" text="ACCOUNT" style="-fx-background-color: #6c757d; -fx-text-fill: white; -fx-background-radius: 20; -fx-font-weight: bold; -fx-font-size: 11px;" />
                  <VBox alignment="CENTER" spacing="2.0" style="-fx-background-color: #f8f9fa; -fx-background-radius: 15; -fx-padding: 8;">
                     <children>
                        <Label fx:id="numMediaInCart" text="0 media" style="-fx-font-weight: bold; -fx-font-size: 12px; -fx-text-fill: #007bff;" />
                        <Label text="in cart" style="-fx-font-size: 10px; -fx-text-fill: #6c757d;" />
                     </children>
                  </VBox>
               </children>
            </HBox>
         </children>
      </HBox>
      <VBox layoutY="103.0" prefHeight="678.0" prefWidth="1326.0" style="-fx-background-color: #D3D3D3;">
         <children>
            <HBox fx:id="hboxMedia" layoutX="24.0" layoutY="123.0" prefHeight="600.0" prefWidth="1327.0" style="-fx-background-color: #D3D3D3;">
               <children>
                  <VBox fx:id="vboxMedia1" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia2" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia3" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia4" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
                  <VBox fx:id="vboxMedia5" prefHeight="600.0" prefWidth="264.0" style="-fx-border-color: gray; -fx-background-color: white;" />
               </children>
            </HBox>
            <HBox alignment="CENTER" layoutX="24.0" layoutY="723.0" prefHeight="50.0" prefWidth="1327.0" spacing="10.0">
               <children>
                  <Button fx:id="btnPrevPage" alignment="CENTER" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Previous" />
                  <Label fx:id="lblPageInfo" alignment="CENTER" contentDisplay="CENTER" text="Page 1 of x" />
                  <Button fx:id="btnNextPage" mnemonicParsing="false" prefHeight="30.0" prefWidth="76.0" text="Next" />
               </children>
            </HBox>
         </children>
      </VBox>
   </children>
</AnchorPane>
